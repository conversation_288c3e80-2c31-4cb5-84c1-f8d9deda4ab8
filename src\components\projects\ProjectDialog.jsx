import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useProjects } from '@/contexts/ProjectContext';
import { 
  Folder, 
  FolderOpen, 
  FileText, 
  Code, 
  Database, 
  Settings,
  Globe,
  Image,
  Music,
  Video
} from 'lucide-react';

const iconOptions = [
  { value: 'Folder', label: 'Folder', icon: Folder },
  { value: 'FolderOpen', label: 'Folder Open', icon: FolderOpen },
  { value: 'FileText', label: 'Document', icon: FileText },
  { value: 'Code', label: 'Code', icon: Code },
  { value: 'Database', label: 'Database', icon: Database },
  { value: 'Settings', label: 'Settings', icon: Settings },
  { value: 'Globe', label: 'Web', icon: Globe },
  { value: 'Image', label: 'Image', icon: Image },
  { value: 'Music', label: 'Music', icon: Music },
  { value: 'Video', label: 'Video', icon: Video },
];

export function ProjectDialog({ open, onOpenChange, project = null, mode = 'create' }) {
  const { createProject, updateProject } = useProjects();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    icon: 'Folder',
    url: '#'
  });
  const [loading, setLoading] = useState(false);

  // Reset form when dialog opens/closes or project changes
  useEffect(() => {
    if (open) {
      if (mode === 'edit' && project) {
        setFormData({
          name: project.name || '',
          description: project.description || '',
          icon: project.icon || 'Folder',
          url: project.url || '#'
        });
      } else {
        setFormData({
          name: '',
          description: '',
          icon: 'Folder',
          url: '#'
        });
      }
    }
  }, [open, project, mode]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      return;
    }

    setLoading(true);
    try {
      if (mode === 'edit' && project) {
        await updateProject(project.id, formData);
      } else {
        await createProject(formData);
      }
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to save project:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const selectedIcon = iconOptions.find(option => option.value === formData.icon);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {mode === 'edit' ? 'Edit Project' : 'Create New Project'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'edit' 
              ? 'Update your project details below.' 
              : 'Add a new project to your workspace.'
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Project Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter project name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter project description (optional)"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="icon">Icon</Label>
            <Select value={formData.icon} onValueChange={(value) => handleInputChange('icon', value)}>
              <SelectTrigger>
                <SelectValue>
                  <div className="flex items-center gap-2">
                    {selectedIcon && <selectedIcon.icon className="h-4 w-4" />}
                    {selectedIcon?.label}
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {iconOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <option.icon className="h-4 w-4" />
                      {option.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="url">URL</Label>
            <Input
              id="url"
              value={formData.url}
              onChange={(e) => handleInputChange('url', e.target.value)}
              placeholder="Enter project URL (optional)"
            />
          </div>

          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !formData.name.trim()}>
              {loading ? 'Saving...' : (mode === 'edit' ? 'Update' : 'Create')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
